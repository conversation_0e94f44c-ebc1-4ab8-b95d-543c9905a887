import React, { useState } from "react";
import { View } from "react-native";
import PropTypes from "prop-types";
import InputField from "../../../components/InputField";
import CollapsibleHeader from "./CollapsibleHeader";
import icons from "../../../assets/icons";

const SocialSection = React.forwardRef(
  ({ form, handleChange, errors }, ref) => {
    const [isSocialsCollapsed, setIsSocialsCollapsed] = useState(false);
    const [isMessengerCollapsed, setIsMessengerCollapsed] = useState(true);

    // Visibility states for social media fields
    const [fieldVisibility, setFieldVisibility] = useState({
      facebook: true,
      instagram: true,
      twitter: true,
      linkedIn: true,
      snapchat: true,
      whatsapp: true,
      telegram: true,
      signal: true,
      youtube: true,
      twitch: true,
      tiktok: true,
      discord: true,
      googleChat: true,
      iMessage: true,
      wechat: true,
      kik: true,
      slack: true,
      line: true,
      skype: true,
    });

    // Handle visibility toggle for a field
    const handleVisibilityToggle = (fieldName) => {
      setFieldVisibility((prev) => ({
        ...prev,
        [fieldName]: !prev[fieldName],
      }));
    };

    // Expose methods through the ref
    React.useImperativeHandle(ref, () => ({
      getFieldVisibility: () => fieldVisibility,
    }));

    return (
      <View>
        <CollapsibleHeader
          title="Socials"
          isCollapsed={isSocialsCollapsed}
          onToggle={() => setIsSocialsCollapsed(!isSocialsCollapsed)}
        />
        {!isSocialsCollapsed && (
          <View style={{ zIndex: 2, position: "relative" }}>
            <InputField
              label="Facebook"
              value={form.facebook}
              onChangeText={(val) => handleChange("facebook", val)}
              error={errors.facebook}
              placeholder="https://facebook.com/username"
              keyboardType="url"
              showVisibilityToggle={true}
              isVisible={fieldVisibility.facebook}
              onVisibilityToggle={() => handleVisibilityToggle("facebook")}
            />
            <InputField
              label="Instagram"
              value={form.instagram}
              onChangeText={(val) => handleChange("instagram", val)}
              error={errors.instagram}
              placeholder="https://instagram.com/username"
              keyboardType="url"
              showVisibilityToggle={true}
              isVisible={fieldVisibility.instagram}
              onVisibilityToggle={() => handleVisibilityToggle("instagram")}
            />
            <InputField
              label="X"
              value={form.twitter}
              onChangeText={(val) => handleChange("twitter", val)}
              error={errors.twitter}
              placeholder="https://twitter.com/username"
              keyboardType="url"
              showVisibilityToggle={true}
              isVisible={fieldVisibility.twitter}
              onVisibilityToggle={() => handleVisibilityToggle("twitter")}
            />
            <InputField
              label="LinkedIn"
              value={form.linkedIn}
              onChangeText={(val) => handleChange("linkedIn", val)}
              error={errors.linkedIn}
              placeholder="https://linkedin.com/in/username"
              keyboardType="url"
              showVisibilityToggle={true}
              isVisible={fieldVisibility.linkedIn}
              onVisibilityToggle={() => handleVisibilityToggle("linkedIn")}
            />
            <InputField
              label="Snapchat"
              value={form.snapchat}
              onChangeText={(val) => handleChange("snapchat", val)}
              error={errors.snapchat}
              placeholder="https://snapchat.com/add/username"
              keyboardType="url"
              showVisibilityToggle={true}
              isVisible={fieldVisibility.snapchat}
              onVisibilityToggle={() => handleVisibilityToggle("snapchat")}
            />
            <InputField
              label="WhatsApp"
              value={form.whatsapp}
              onChangeText={(val) => handleChange("whatsapp", val)}
              error={errors.whatsapp}
              placeholder="https://wa.me/phonenumber"
              keyboardType="url"
              showVisibilityToggle={true}
              isVisible={fieldVisibility.whatsapp}
              onVisibilityToggle={() => handleVisibilityToggle("whatsapp")}
            />
            <InputField
              label="Telegram"
              value={form.telegram}
              onChangeText={(val) => handleChange("telegram", val)}
              error={errors.telegram}
              placeholder="https://t.me/username"
              keyboardType="url"
              showVisibilityToggle={true}
              isVisible={fieldVisibility.telegram}
              onVisibilityToggle={() => handleVisibilityToggle("telegram")}
            />
            <InputField
              label="Signal"
              value={form.signal}
              onChangeText={(val) => handleChange("signal", val)}
              error={errors.signal}
              placeholder="signal://username"
              keyboardType="url"
              showVisibilityToggle={true}
              isVisible={fieldVisibility.signal}
              onVisibilityToggle={() => handleVisibilityToggle("signal")}
            />

            <InputField
              label="YouTube"
              value={form.youtube}
              onChangeText={(val) => handleChange("youtube", val)}
              error={errors.youtube}
              placeholder="https://youtube.com/c/channelname"
              keyboardType="url"
              showVisibilityToggle={true}
              isVisible={fieldVisibility.youtube}
              onVisibilityToggle={() => handleVisibilityToggle("youtube")}
            />
            <InputField
              label="Twitch"
              value={form.twitch}
              onChangeText={(val) => handleChange("twitch", val)}
              error={errors.twitch}
              placeholder="https://twitch.tv/username"
              keyboardType="url"
              showVisibilityToggle={true}
              isVisible={fieldVisibility.twitch}
              onVisibilityToggle={() => handleVisibilityToggle("twitch")}
            />
            <InputField
              label="TikTok"
              value={form.tiktok}
              onChangeText={(val) => handleChange("tiktok", val)}
              error={errors.tiktok}
              placeholder="https://tiktok.com/@username"
              keyboardType="url"
              showVisibilityToggle={true}
              isVisible={fieldVisibility.tiktok}
              onVisibilityToggle={() => handleVisibilityToggle("tiktok")}
            />
            {/* snapchat, whatsapp, telegram, signal, youtube, tiktok, twitch, discord, googleChat, Imessage, wechat, kik, slack, line, skype, */}
            <InputField
              label="Discord"
              value={form.discord}
              onChangeText={(val) => handleChange("discord", val)}
              error={errors.discord}
              placeholder="https://discord.com/invite/username"
              keyboardType="url"
              showVisibilityToggle={true}
              isVisible={fieldVisibility.discord}
              onVisibilityToggle={() => handleVisibilityToggle("discord")}
            />
            <InputField
              label="Google Chat"
              value={form.googleChat}
              onChangeText={(val) => handleChange("googleChat", val)}
              error={errors.googleChat}
              placeholder="https://chat.google.com/username"
              keyboardType="url"
              showVisibilityToggle={true}
              isVisible={fieldVisibility.googleChat}
              onVisibilityToggle={() => handleVisibilityToggle("googleChat")}
            />

            <InputField
              label="WeChat"
              value={form.wechat}
              onChangeText={(val) => handleChange("wechat", val)}
              error={errors.wechat}
              placeholder="https://wechat.com/username"
              keyboardType="url"
              showVisibilityToggle={true}
              isVisible={fieldVisibility.wechat}
              onVisibilityToggle={() => handleVisibilityToggle("wechat")}
            />
            <InputField
              label="Kik"
              value={form.kik}
              onChangeText={(val) => handleChange("kik", val)}
              error={errors.kik}
              placeholder="https://kik.com/username"
              keyboardType="url"
              showVisibilityToggle={true}
              isVisible={fieldVisibility.kik}
              onVisibilityToggle={() => handleVisibilityToggle("kik")}
            />
            <InputField
              label="Slack"
              value={form.slack}
              onChangeText={(val) => handleChange("slack", val)}
              error={errors.slack}
              placeholder="https://slack.com/username"
              keyboardType="url"
              showVisibilityToggle={true}
              isVisible={fieldVisibility.slack}
              onVisibilityToggle={() => handleVisibilityToggle("slack")}
            />
            <InputField
              label="Line"
              value={form.line}
              onChangeText={(val) => handleChange("line", val)}
              error={errors.line}
              placeholder="https://line.me/username"
              keyboardType="url"
              showVisibilityToggle={true}
              isVisible={fieldVisibility.line}
              onVisibilityToggle={() => handleVisibilityToggle("line")}
            />
            <InputField
              label="Skype"
              value={form.skype}
              onChangeText={(val) => handleChange("skype", val)}
              error={errors.skype}
              placeholder="https://skype.com/username"
              keyboardType="url"
              showVisibilityToggle={true}
              isVisible={fieldVisibility.skype}
              onVisibilityToggle={() => handleVisibilityToggle("skype")}
            />
          </View>
        )}

        <CollapsibleHeader
          title="Personal Messenger IDs"
          isCollapsed={isMessengerCollapsed}
          onToggle={() => setIsMessengerCollapsed(!isMessengerCollapsed)}
        />
        {!isMessengerCollapsed && (
          <View style={{ zIndex: 1, position: "relative" }}>
            <InputField
              label="iMessage"
              value={form.iMessage}
              onChangeText={(val) => handleChange("iMessage", val)}
              error={errors.iMessage}
              placeholder="https://imessage.com/username"
              keyboardType="url"
              showVisibilityToggle={true}
              isVisible={fieldVisibility.iMessage}
              onVisibilityToggle={() => handleVisibilityToggle("iMessage")}
            />
          </View>
        )}
      </View>
    );
  }
);

SocialSection.propTypes = {
  form: PropTypes.shape({
    facebook: PropTypes.string,
    instagram: PropTypes.string,
    twitter: PropTypes.string,
    linkedIn: PropTypes.string,
    snapchat: PropTypes.string,
    whatsapp: PropTypes.string,
    telegram: PropTypes.string,
    signal: PropTypes.string,
    youtube: PropTypes.string,
    twitch: PropTypes.string,
    tiktok: PropTypes.string,
  }).isRequired,
  handleChange: PropTypes.func.isRequired,
  errors: PropTypes.object,
};

export default SocialSection;
